/* Product Tabs Styles */
.product-tabs-wrapper {
  margin-top: 4rem;
  padding: 0 2rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.product-tabs {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.product-tabs__nav {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.product-tabs__tab {
  background: none;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.4rem;
  font-weight: 500;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0;
  border-bottom: 3px solid transparent;
  position: relative;
}

.product-tabs__tab:hover {
  color: #495057;
  background: rgba(0, 123, 255, 0.05);
}

.product-tabs__tab.active {
  color: #007bff;
  background: #ffffff;
  border-bottom-color: #007bff;
  font-weight: 600;
}

.product-tabs__content {
  padding: 2rem;
}

.product-tabs__panel {
  display: none;
}

.product-tabs__panel.active {
  display: block;
}

/* Overview Content Styles */
.overview-content {
  max-width: 100%;
}

.overview-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  margin-top: 3rem;
}

.overview-content h3:first-child {
  margin-top: 0;
}

/* Highlights Section */
.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.highlight-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  flex-shrink: 0;
}

.highlight-content h4 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

/* Amenities Section */
.amenities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.amenity-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  font-size: 1.4rem;
  color: #475569;
}

.amenity-item svg {
  color: #64748b;
  flex-shrink: 0;
}

.amenities-show-all {
  background: none;
  border: 1px solid #3b82f6;
  color: #3b82f6;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1.4rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.amenities-show-all:hover {
  background: #3b82f6;
  color: white;
}

/* Description Section */
.description-content {
  font-size: 1.4rem;
  line-height: 1.6;
  color: #475569;
}

.description-show-more {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 1.4rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  margin-top: 1rem;
  padding: 0;
}

.description-show-more:hover {
  color: #1d4ed8;
}

/* Surroundings Section */
.surroundings-content {
  font-size: 1.4rem;
}

.surroundings-item {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: #475569;
}

.surroundings-item strong {
  color: #1e293b;
  min-width: 80px;
}

.surroundings-view-map {
  background: none;
  border: 1px solid #3b82f6;
  color: #3b82f6;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1.4rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.surroundings-view-map:hover {
  background: #3b82f6;
  color: white;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .product-tabs-wrapper {
    padding: 0 1rem;
    margin-top: 2rem;
  }
  
  .product-tabs__content {
    padding: 1.5rem;
  }
  
  .product-tabs__tab {
    padding: 0.75rem 1rem;
    font-size: 1.3rem;
  }
  
  .highlights-grid,
  .amenities-grid {
    grid-template-columns: 1fr;
  }
  
  .overview-content h3 {
    font-size: 1.8rem;
  }
}

@media screen and (max-width: 480px) {
  .product-tabs__tab {
    padding: 0.5rem 0.75rem;
    font-size: 1.2rem;
  }
  
  .product-tabs__content {
    padding: 1rem;
  }
  
  .highlight-item,
  .amenity-item {
    padding: 0.5rem;
  }
}

/* Rating Section (if needed) */
.overview-rating {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bae6fd;
}

.rating-score {
  font-size: 2.4rem;
  font-weight: 700;
  color: #0369a1;
}

.rating-text {
  font-size: 1.6rem;
  font-weight: 600;
  color: #0369a1;
}

.rating-subtitle {
  font-size: 1.3rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.rating-reviews {
  font-size: 1.3rem;
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
}

.rating-reviews:hover {
  color: #1d4ed8;
}
